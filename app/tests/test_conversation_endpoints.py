import copy
from unittest.mock import AsyncMock, patch
from uuid import uuid4

from deepdiff import DeepDiff
from fastapi import status
import pytest

from constants.message import WELCOME_MESSAGE, MessageRole, MessageType, SuggestedUserPrompt
from constants.operation_ids import operation_ids
from core.urls import URLResolver
from dependencies import CustomAsyncClient
from exceptions.entity import EntityNotFoundError


TEST_MESSAGE_DATA = [
    {
        'role': MessageRole.USER,
        'type': MessageType.TEXT,
        'content': 'User message 1',
    },
    {
        'role': MessageRole.USER,
        'type': MessageType.TEXT,
        'content': 'Assistant response 1',
    },
    {
        'role': MessageRole.USER,
        'type': MessageType.TEXT,
        'content': 'User message 2',
    },
]


async def test_create_conversation_success(
    mock_kx_dash_service,
    auth_mock,
    auth_header,
    decoded_jwt_token,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
):
    """Test that a conversation can be created successfully."""
    # Mock KX dash service to return no tasks (using global mock)
    mock_kx_dash_service.list.return_value = []

    url = url_resolver.reverse(operation_ids.conversation.CREATE)

    response = await async_client.post(url, headers=auth_header, json=conversation_data)
    data = response.json()

    assert response.status_code == status.HTTP_201_CREATED, response.json()

    # Verify the conversation data
    conversation = data['conversation']
    assert conversation['from_dash'] == conversation_data['from_dash']
    assert conversation['created_by_id'] == decoded_jwt_token['oid']
    assert conversation['created_by_name'] == decoded_jwt_token['name']
    assert not conversation['is_completed']
    assert conversation['qual_id'] is None  # qual_id should be None by default
    assert 'id' in conversation
    assert 'created_at' in conversation

    # Verify welcome message exists with expected fields
    welcome_message = data['welcome_message']

    expected = {
        'conversation': dict(
            **conversation_data,
            qual_id=None,
            created_by_id=decoded_jwt_token['oid'],
            created_by_name=decoded_jwt_token['name'],
            id=conversation['id'],
            is_completed=False,
            created_at=conversation['created_at'],
        ),
        'welcome_message': {
            'id': welcome_message['id'],
            'conversation_id': conversation['id'],
            'role': str(MessageRole.SYSTEM),
            'type': str(MessageType.TEXT),
            'content': WELCOME_MESSAGE,
            'options': [],
            'created_at': welcome_message['created_at'],
            'suggested_prompts': [
                SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION.value,
                SuggestedUserPrompt.UPLOAD_DOCUMENT.value,
            ],
        },
    }

    assert data == expected, DeepDiff(expected, data, ignore_order=True)


async def test_get_conversation_success(
    auth_mock,
    auth_header,
    decoded_jwt_token,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
):
    """Test that a conversation can be retrieved successfully."""
    create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    create_response = await async_client.post(create_url, headers=auth_header, json=conversation_data)
    conversation_id = create_response.json()['conversation']['id']
    get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=conversation_id)
    get_response = await async_client.get(get_url, headers=auth_header)

    assert get_response.status_code == status.HTTP_200_OK

    data = get_response.json()
    expected_response = dict(
        **conversation_data,
        qual_id=None,
        created_by_id=decoded_jwt_token['oid'],
        created_by_name=decoded_jwt_token['name'],
        id=conversation_id,
        is_completed=False,
        created_at=data['created_at'],
    )
    assert data == expected_response, DeepDiff(expected_response, data, ignore_order=True)


async def test_get_conversation_last_message_not_found(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
):
    """Test that a conversation get last fails properly."""
    create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    create_response = await async_client.post(create_url, headers=auth_header, json=conversation_data)
    conversation_id = create_response.json()['conversation']['id']
    get_url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=conversation_id)

    with (
        patch('services.conversation.ConversationMessageService.get_last') as get_conversation_last_mock,
    ):
        get_conversation_last_mock.side_effect = EntityNotFoundError(
            'The last message of conversation', conversation_id
        )

        get_response = await async_client.get(get_url, headers=auth_header)

    assert get_response.status_code == status.HTTP_404_NOT_FOUND


async def test_get_conversation_not_found(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
):
    """Test that a conversation cannot be retrieved if it does not exist."""
    non_existent_id = str(uuid4())
    url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=non_existent_id)

    response = await async_client.get(url, headers=auth_header)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    expected = {'detail': f'Conversation with ID {non_existent_id} not found'}
    assert response.json() == expected


async def test_get_conversation_invalid_uuid(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
):
    """Test that a conversation cannot be retrieved if the UUID is invalid."""
    url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id='not-a-uuid')

    response = await async_client.get(url, headers=auth_header)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


async def test_get_conversation_invalid_user_uuid(
    auth_mock,
    auth_header,
    decoded_jwt_token,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
):
    """Test that a conversation cannot be retrieved if the UUID is invalid."""
    create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    create_response = await async_client.post(create_url, headers=auth_header, json=conversation_data)
    conversation_id = create_response.json()['conversation']['id']

    with (
        patch('middleware.auth.AzureADAuthorizerMiddleware._decode_token') as decode_token_mock,
    ):
        decode_token_mock.new_callable = AsyncMock
        decode_token_mock.return_value = copy.deepcopy(decoded_jwt_token)
        decode_token_mock.return_value['oid'] = str(uuid4())

        get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=conversation_id)
        get_response = await async_client.get(get_url, headers=auth_header)

    assert get_response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.parametrize(
    'invalid_data',
    (
        {},
        {'from_dash': 'not-a-boolean'},
    ),
)
async def test_create_conversation_validation_errors(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    invalid_data: dict[str, str] | dict[str, int | str | None],
):
    """Test that a conversation cannot be created with invalid data."""
    url = url_resolver.reverse(operation_ids.conversation.CREATE)

    response = await async_client.post(url, headers=auth_header, json=invalid_data)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


async def test_delete_conversation_basic_functionality(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
):
    """Test basic conversation deletion functionality - delete request returns 204 and the conversation no longer exists."""
    create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    create_response = await async_client.post(create_url, headers=auth_header, json=conversation_data)
    conversation_id = create_response.json()['conversation']['id']

    delete_url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id=conversation_id)
    delete_response = await async_client.delete(delete_url, headers=auth_header)

    get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=conversation_id)
    get_response = await async_client.get(get_url, headers=auth_header)

    assert delete_response.status_code == status.HTTP_204_NO_CONTENT
    assert delete_response.content == b''  # No content in response body
    assert get_response.status_code == status.HTTP_404_NOT_FOUND


async def test_delete_conversation_not_found(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
):
    """Test that a conversation cannot be deleted if it does not exist."""
    non_existent_id = str(uuid4())
    url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id=non_existent_id)

    response = await async_client.delete(url, headers=auth_header)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    expected = {'detail': f'Conversation with ID {non_existent_id} not found'}
    assert response.json() == expected


async def test_delete_conversation_invalid_uuid(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
):
    """Test that a conversation cannot be deleted if the UUID is invalid."""
    url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id='not-a-uuid')

    response = await async_client.delete(url, headers=auth_header)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


async def test_get_conversation_messages_success(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
):
    """Test that conversation messages can be retrieved successfully."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']
    welcome_message_id = conversation_response.json()['welcome_message']['id']

    message_create_url = url_resolver.reverse(operation_ids.message.CREATE)
    message_ids = [welcome_message_id]
    for message in TEST_MESSAGE_DATA:
        form_data = {'conversation_id': str(conversation_id), 'content': message['content']}
        response = await async_client.post(message_create_url, headers=auth_header, data=form_data)
        assert response.status_code == status.HTTP_201_CREATED, response.json()
        message_ids.append(response.json()['user']['id'])

    messages_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=conversation_id)
    messages_response = await async_client.get(messages_url, headers=auth_header)

    assert messages_response.status_code == status.HTTP_200_OK, messages_response.json()

    messages = messages_response.json()
    assert len(messages) == 4  # Welcome message + 3 user messages
    assert messages[0]['id'] == welcome_message_id  # First message should be the welcome message

    # Instead of directly comparing with TEST_MESSAGE_DATA, verify all expected messages are present
    message_contents = {message['content'] for message in messages}
    for test_data in TEST_MESSAGE_DATA:
        content_is_matching = False
        content = test_data['content']
        for row in message_contents:
            if content in row:
                content_is_matching = True
                break
        assert content_is_matching

    for message in messages:
        assert message['conversation_id'] == conversation_id


async def test_get_conversation_last_message(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Test that the last message of a conversation can be retrieved successfully."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    message_create_url = url_resolver.reverse(operation_ids.message.CREATE)
    for data in TEST_MESSAGE_DATA:
        form_data = {'conversation_id': str(conversation_id), 'content': data['content']}
        response = await async_client.post(message_create_url, headers=auth_header, data=form_data)
        assert response.status_code == status.HTTP_201_CREATED

    messages_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=conversation_id)
    messages_response = await async_client.get(messages_url, headers=auth_header)

    assert messages_response.status_code == status.HTTP_200_OK, messages_response.json()
    messages = messages_response.json()

    # The last message should be the last one created
    expected_latest_message = messages[-1]

    last_message_url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=conversation_id)
    last_message_response = await async_client.get(last_message_url, headers=auth_header)

    last_message = last_message_response.json()

    assert last_message['conversation_id'] == expected_latest_message['conversation_id']
    assert last_message['id'] == expected_latest_message['id']


async def test_get_conversation_messages_not_found(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
):
    """Test that conversation messages cannot be retrieved if the conversation does not exist."""
    nonexistent_id = str(uuid4())
    messages_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=nonexistent_id)

    response = await async_client.get(messages_url, headers=auth_header)

    assert response.status_code == status.HTTP_404_NOT_FOUND

    create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    create_response = await async_client.post(create_url, headers=auth_header, json=conversation_data)
    conversation_id = create_response.json()['conversation']['id']
    get_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=conversation_id)

    with (
        patch('repositories.conversation.ConversationRepository.exists') as get_conversation_list_mock,
    ):
        get_conversation_list_mock.side_effect = EntityNotFoundError('Conversation', conversation_id)

        get_response = await async_client.get(get_url, headers=auth_header)

    assert get_response.status_code == status.HTTP_404_NOT_FOUND


async def test_get_conversation_messages_malformed_uuid(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
):
    """Test that conversation messages cannot be retrieved if the UUID is invalid."""
    invalid_id = 'not-a-uuid'
    messages_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=invalid_id)

    response = await async_client.get(messages_url, headers=auth_header)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert 'detail' in response.json()


async def test_get_conversation_messages_wrong_user_id(
    auth_mock,
    auth_header,
    decoded_jwt_token,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
):
    """Test that conversation messages cannot be retrieved if the user ID is incorrect."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']
    nonexistent_user_id = str(uuid4())
    with (
        patch('middleware.auth.AzureADAuthorizerMiddleware._decode_token') as decode_token_mock,
    ):
        decode_token_mock.new_callable = AsyncMock
        decode_token_mock.return_value = copy.deepcopy(decoded_jwt_token)
        decode_token_mock.return_value['oid'] = nonexistent_user_id

        messages_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=conversation_id)
        messages_response = await async_client.get(messages_url, headers=auth_header)

    assert messages_response.status_code == status.HTTP_404_NOT_FOUND


async def test_delete_conversation_deletes_all_messages(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
):
    """Test that deleting a conversation also deletes all associated messages."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    assert conversation_response.status_code == status.HTTP_201_CREATED
    conversation_id = conversation_response.json()['conversation']['id']

    message_create_url = url_resolver.reverse(operation_ids.message.CREATE)
    for data in TEST_MESSAGE_DATA:
        form_data = {'conversation_id': str(conversation_id), 'content': data['content']}
        response = await async_client.post(message_create_url, headers=auth_header, data=form_data)
        assert response.status_code == status.HTTP_201_CREATED, response.json()

    messages_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=conversation_id)
    messages_response = await async_client.get(messages_url, headers=auth_header)

    delete_url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id=conversation_id)
    delete_response = await async_client.delete(delete_url, headers=auth_header)

    get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=conversation_id)
    get_response = await async_client.get(get_url, headers=auth_header)

    messages_response_after = await async_client.get(messages_url, headers=auth_header)

    assert messages_response.status_code == status.HTTP_200_OK, messages_response.json()
    assert len(messages_response.json()) == 4  # 1 welcome + 3 user messages

    assert delete_response.status_code == status.HTTP_204_NO_CONTENT

    assert get_response.status_code == status.HTTP_404_NOT_FOUND
    assert messages_response_after.status_code == status.HTTP_404_NOT_FOUND
    assert messages_response_after.json()['detail'] == f'Conversation with ID {conversation_id} not found'


async def test_delete_all_conversation_messages_wrong_user_id(
    auth_mock,
    auth_header,
    decoded_jwt_token,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
):
    """Test that a message cannot be deleted if the user ID is incorrect."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']

    message_create_url = url_resolver.reverse(operation_ids.message.CREATE)
    form_data = {'conversation_id': str(conversation_id), 'content': 'Test message content'}
    response = await async_client.post(message_create_url, headers=auth_header, data=form_data)
    assert response.status_code == status.HTTP_201_CREATED

    with (
        patch('middleware.auth.AzureADAuthorizerMiddleware._decode_token') as decode_token_mock,
    ):
        decode_token_mock.new_callable = AsyncMock
        decode_token_mock.return_value = copy.deepcopy(decoded_jwt_token)
        decode_token_mock.return_value['oid'] = str(uuid4())

        delete_url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id=conversation_id)
        delete_response = await async_client.delete(delete_url, headers=auth_header)

    assert delete_response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.parametrize(
    'headers,error_message',
    [
        ({}, 'Authorization header is missing or empty'),  # missing header
        ({'Authorization': 'NoBearer invalid_token'}, 'Wrong authorization scheme'),  # wrong token scheme
        ({'Authorization': 'Bearer'}, 'Token is missing'),  # missing token
    ],
)
async def test_create_conversation_wrong_authentication(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    conversation_data: dict,
    headers,
    error_message,
):
    create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    create_response = await async_client.post(create_url, headers=auth_header, json=conversation_data)
    conversation_id = create_response.json()['conversation']['id']

    get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=conversation_id)
    get_response = await async_client.get(get_url, headers=headers)

    assert get_response.status_code == status.HTTP_401_UNAUTHORIZED
    assert get_response.json() == error_message


@pytest.mark.parametrize(
    'mocked_scp,error_message',
    [
        ({'scp': None}, 'Malformed scopes'),  # malformed scopes
        ({}, 'Missing a required scope'),  # missing required scope
    ],
)
async def test_create_conversation_wrong_authentication_token_scopes(
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver: URLResolver,
    mocked_scp,
    error_message,
):
    get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=str(uuid4()))

    with patch('jose.jwt.get_unverified_claims') as get_claims_mock:
        get_claims_mock.new_callable = AsyncMock

        get_claims_mock.return_value = mocked_scp
        get_response = await async_client.get(get_url, headers=auth_header)

    assert get_response.status_code == status.HTTP_401_UNAUTHORIZED
    assert get_response.json() == error_message
